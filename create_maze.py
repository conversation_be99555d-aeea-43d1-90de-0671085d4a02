import socket
import json

def send_blender_command(command_type, params=None):
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.connect(('localhost', 9876))
    cmd = {'type': command_type}
    if params:
        cmd['params'] = params
    s.send(json.dumps(cmd).encode())
    response = s.recv(4096)
    s.close()
    return json.loads(response.decode())

# Create boundary walls
boundary_code = '''
import bpy
from mathutils import Vector

wall_center = bpy.data.objects["SM_Wall_Center_01"]
wall_half = bpy.data.objects["SM_Wall_Half_01"]
wall_quarter = bpy.data.objects["SM_Wall_Quarter_01"]

BASE_WIDTH = 4.96
MAZE_SIZE = 8
wall_count = 0

def duplicate_wall(original_wall, location, rotation_z=0, name_suffix=""):
    global wall_count
    new_obj = original_wall.copy()
    new_obj.data = original_wall.data.copy()
    new_obj.name = f"{original_wall.name}_maze_{wall_count}{name_suffix}"
    new_obj.location = location
    new_obj.rotation_euler = (0, 0, rotation_z)
    bpy.context.collection.objects.link(new_obj)
    wall_count += 1
    return new_obj

print("Creating boundary walls...")

# Bottom and top boundaries
for i in range(MAZE_SIZE + 1):
    x = i * BASE_WIDTH
    duplicate_wall(wall_center, Vector((x, 0, 0)), 0, f"_bottom_{i}")
    duplicate_wall(wall_center, Vector((x, MAZE_SIZE * BASE_WIDTH, 0)), 0, f"_top_{i}")

# Left and right boundaries  
for i in range(MAZE_SIZE + 1):
    y = i * BASE_WIDTH
    duplicate_wall(wall_center, Vector((0, y, 0)), 1.5708, f"_left_{i}")
    duplicate_wall(wall_center, Vector((MAZE_SIZE * BASE_WIDTH, y, 0)), 1.5708, f"_right_{i}")

print(f"Boundary created with {wall_count} walls")
'''

print('Creating boundary walls...')
result = send_blender_command('execute_code', {'code': boundary_code})
print('Boundary walls created!')

# Create internal maze walls using all three wall types
internal_walls_code = '''
import bpy
from mathutils import Vector

wall_center = bpy.data.objects["SM_Wall_Center_01"]
wall_half = bpy.data.objects["SM_Wall_Half_01"]
wall_quarter = bpy.data.objects["SM_Wall_Quarter_01"]

BASE_WIDTH = 4.96
wall_count = 36  # Continue from boundary walls

def duplicate_wall(original_wall, location, rotation_z=0, name_suffix=""):
    global wall_count
    new_obj = original_wall.copy()
    new_obj.data = original_wall.data.copy()
    new_obj.name = f"{original_wall.name}_maze_{wall_count}{name_suffix}"
    new_obj.location = location
    new_obj.rotation_euler = (0, 0, rotation_z)
    bpy.context.collection.objects.link(new_obj)
    wall_count += 1
    return new_obj

print("Creating internal maze walls...")

# Create simple maze pattern using all three wall types
# Horizontal walls (using different wall types)
duplicate_wall(wall_center, Vector((2*BASE_WIDTH, 1*BASE_WIDTH, 0)), 0, "_h1")
duplicate_wall(wall_half, Vector((4*BASE_WIDTH, 1*BASE_WIDTH, 0)), 0, "_h2")
duplicate_wall(wall_quarter, Vector((6*BASE_WIDTH, 1*BASE_WIDTH, 0)), 0, "_h3")

duplicate_wall(wall_quarter, Vector((1*BASE_WIDTH, 3*BASE_WIDTH, 0)), 0, "_h4")
duplicate_wall(wall_center, Vector((3*BASE_WIDTH, 3*BASE_WIDTH, 0)), 0, "_h5")
duplicate_wall(wall_half, Vector((5*BASE_WIDTH, 3*BASE_WIDTH, 0)), 0, "_h6")
duplicate_wall(wall_center, Vector((7*BASE_WIDTH, 3*BASE_WIDTH, 0)), 0, "_h7")

duplicate_wall(wall_half, Vector((2*BASE_WIDTH, 5*BASE_WIDTH, 0)), 0, "_h8")
duplicate_wall(wall_center, Vector((4*BASE_WIDTH, 5*BASE_WIDTH, 0)), 0, "_h9")
duplicate_wall(wall_quarter, Vector((6*BASE_WIDTH, 5*BASE_WIDTH, 0)), 0, "_h10")

duplicate_wall(wall_center, Vector((1*BASE_WIDTH, 7*BASE_WIDTH, 0)), 0, "_h11")
duplicate_wall(wall_quarter, Vector((3*BASE_WIDTH, 7*BASE_WIDTH, 0)), 0, "_h12")
duplicate_wall(wall_half, Vector((5*BASE_WIDTH, 7*BASE_WIDTH, 0)), 0, "_h13")

# Vertical walls (using different wall types)
duplicate_wall(wall_half, Vector((1*BASE_WIDTH, 2*BASE_WIDTH, 0)), 1.5708, "_v1")
duplicate_wall(wall_center, Vector((3*BASE_WIDTH, 2*BASE_WIDTH, 0)), 1.5708, "_v2")
duplicate_wall(wall_quarter, Vector((5*BASE_WIDTH, 2*BASE_WIDTH, 0)), 1.5708, "_v3")
duplicate_wall(wall_center, Vector((7*BASE_WIDTH, 2*BASE_WIDTH, 0)), 1.5708, "_v4")

duplicate_wall(wall_quarter, Vector((2*BASE_WIDTH, 4*BASE_WIDTH, 0)), 1.5708, "_v5")
duplicate_wall(wall_half, Vector((4*BASE_WIDTH, 4*BASE_WIDTH, 0)), 1.5708, "_v6")
duplicate_wall(wall_center, Vector((6*BASE_WIDTH, 4*BASE_WIDTH, 0)), 1.5708, "_v7")

duplicate_wall(wall_center, Vector((1*BASE_WIDTH, 6*BASE_WIDTH, 0)), 1.5708, "_v8")
duplicate_wall(wall_quarter, Vector((3*BASE_WIDTH, 6*BASE_WIDTH, 0)), 1.5708, "_v9")
duplicate_wall(wall_half, Vector((5*BASE_WIDTH, 6*BASE_WIDTH, 0)), 1.5708, "_v10")
duplicate_wall(wall_quarter, Vector((7*BASE_WIDTH, 6*BASE_WIDTH, 0)), 1.5708, "_v11")

print(f"Internal walls created, total walls: {wall_count}")
'''

print('Creating internal maze walls...')
result = send_blender_command('execute_code', {'code': internal_walls_code})
print('Internal walls created!')

# Create entrance and exit
entrance_exit_code = '''
import bpy

print("Creating entrance and exit...")

# Find and remove entrance wall (bottom boundary, position 1)
entrance_wall = None
for obj in bpy.context.scene.objects:
    if "bottom_1" in obj.name and "maze" in obj.name:
        entrance_wall = obj
        break
if entrance_wall:
    bpy.data.objects.remove(entrance_wall, do_unlink=True)
    print("Entrance created at bottom")

# Find and remove exit wall (top boundary, position 7)
exit_wall = None
for obj in bpy.context.scene.objects:
    if "top_7" in obj.name and "maze" in obj.name:
        exit_wall = obj
        break
if exit_wall:
    bpy.data.objects.remove(exit_wall, do_unlink=True)
    print("Exit created at top")

print("Entrance and exit created!")
'''

print('Creating entrance and exit...')
result = send_blender_command('execute_code', {'code': entrance_exit_code})
print('Entrance and exit created!')

# Create solution path with cylinder and setup scene
solution_setup_code = '''
import bpy
from mathutils import Vector

BASE_WIDTH = 4.96
MAZE_SIZE = 8

print("Creating solution path...")

# Simple solution path coordinates (from entrance to exit)
solution_path = [
    (1*BASE_WIDTH, 0.5*BASE_WIDTH),  # Start at entrance
    (1*BASE_WIDTH, 2*BASE_WIDTH),
    (2*BASE_WIDTH, 2*BASE_WIDTH),
    (2*BASE_WIDTH, 4*BASE_WIDTH),
    (3*BASE_WIDTH, 4*BASE_WIDTH),
    (3*BASE_WIDTH, 5*BASE_WIDTH),
    (5*BASE_WIDTH, 5*BASE_WIDTH),
    (5*BASE_WIDTH, 6*BASE_WIDTH),
    (6*BASE_WIDTH, 6*BASE_WIDTH),
    (6*BASE_WIDTH, 7*BASE_WIDTH),
    (7*BASE_WIDTH, 7*BASE_WIDTH),
    (7*BASE_WIDTH, 7.5*BASE_WIDTH)   # End at exit
]

# Create cylinders for solution path
for i, (x, y) in enumerate(solution_path):
    bpy.ops.mesh.primitive_cylinder_add(
        radius=0.5,
        depth=1.0,
        location=(x, y, 1.0)
    )

    cylinder = bpy.context.active_object
    cylinder.name = f"PathMarker_{i}"

    # Create bright green material
    if not bpy.data.materials.get("PathMaterial"):
        mat = bpy.data.materials.new(name="PathMaterial")
        mat.use_nodes = True
        mat.node_tree.nodes.clear()

        bsdf = mat.node_tree.nodes.new(type="ShaderNodeBsdfPrincipled")
        output = mat.node_tree.nodes.new(type="ShaderNodeOutputMaterial")

        # Bright green color
        bsdf.inputs["Base Color"].default_value = (0.0, 1.0, 0.0, 1.0)
        bsdf.inputs["Emission"].default_value = (0.0, 0.8, 0.0, 1.0)
        bsdf.inputs["Emission Strength"].default_value = 1.0

        mat.node_tree.links.new(bsdf.outputs["BSDF"], output.inputs["Surface"])

    # Apply material
    cylinder.data.materials.append(bpy.data.materials["PathMaterial"])

print(f"Created {len(solution_path)} path markers")

# Setup camera for overhead view
bpy.ops.object.camera_add()
camera = bpy.context.active_object
camera.name = "MazeCamera"

# Position camera overhead
maze_center = MAZE_SIZE * BASE_WIDTH / 2
camera.location = Vector((maze_center, maze_center, 60))
camera.rotation_euler = (0, 0, 0)
bpy.context.scene.camera = camera

# Add lighting
bpy.ops.object.light_add(type="SUN")
sun = bpy.context.active_object
sun.location = Vector((maze_center, maze_center, 50))
sun.data.energy = 5

print("Scene setup complete!")
print(f"Maze size: {MAZE_SIZE * BASE_WIDTH} x {MAZE_SIZE * BASE_WIDTH} units")
print("Green cylinders show the solution path from entrance to exit")
'''

print('Creating solution path and setting up scene...')
result = send_blender_command('execute_code', {'code': solution_setup_code})
print('Solution path and scene setup complete!')

# Take a screenshot
print('Taking screenshot...')
screenshot_result = send_blender_command('get_viewport_screenshot', {
    'max_size': 1200,
    'filepath': 'maze_with_solution.png',
    'format': 'png'
})
print('Screenshot taken!')
