import socket
import json
import random

def send_blender_command(command_type, params=None):
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.connect(('localhost', 9876))
    cmd = {'type': command_type}
    if params:
        cmd['params'] = params
    s.send(json.dumps(cmd).encode())
    response = s.recv(4096)
    s.close()
    return json.loads(response.decode())

# Clear everything first
clear_code = '''
import bpy

# Keep only the original 3 walls
original_walls = ['SM_Wall_Center_01', 'SM_Wall_Half_01', 'SM_Wall_Quarter_01']
to_delete = []

for obj in bpy.context.scene.objects:
    if obj.name not in original_walls:
        to_delete.append(obj)

print(f'Clearing {len(to_delete)} objects...')
for obj in to_delete:
    bpy.data.objects.remove(obj, do_unlink=True)

print('Scene cleared - only original walls remain')
'''

print('Clearing scene...')
result = send_blender_command('execute_code', {'code': clear_code})
print('Scene cleared!')

# Generate proper maze using recursive backtracking
def generate_maze(width, height):
    """Generate maze using recursive backtracking algorithm"""
    # Create grid - True means wall exists, False means passage
    maze = [[True for _ in range(width)] for _ in range(height)]

    # Directions: North, East, South, West
    directions = [(0, -1), (1, 0), (0, 1), (-1, 0)]

    def carve_passages_from(x, y):
        maze[y][x] = False  # Mark current cell as passage

        # Randomize directions
        dirs = directions.copy()
        random.shuffle(dirs)

        for dx, dy in dirs:
            nx, ny = x + dx * 2, y + dy * 2  # Move 2 cells to skip wall

            # Check if new position is valid and unvisited
            if (0 <= nx < width and 0 <= ny < height and maze[ny][nx]):
                # Carve passage to new cell
                maze[y + dy][x + dx] = False  # Remove wall between cells
                carve_passages_from(nx, ny)  # Recursively continue

    # Start from random odd position (ensures we start in a cell, not wall)
    start_x = random.randrange(1, width, 2)
    start_y = random.randrange(1, height, 2)
    carve_passages_from(start_x, start_y)

    return maze

# Generate maze
MAZE_WIDTH = 21  # Must be odd for proper maze generation
MAZE_HEIGHT = 21
random.seed(42)  # For reproducible maze
maze_grid = generate_maze(MAZE_WIDTH, MAZE_HEIGHT)

print(f'Generated maze: {MAZE_WIDTH}x{MAZE_HEIGHT}')
print('Creating maze in Blender...')

# Create proper maze walls in Blender using the generated maze
maze_creation_code = f'''
import bpy
from mathutils import Vector

# Get wall templates
wall_center = bpy.data.objects["SM_Wall_Center_01"]
wall_half = bpy.data.objects["SM_Wall_Half_01"]
wall_quarter = bpy.data.objects["SM_Wall_Quarter_01"]

# Maze parameters
CELL_SIZE = 2.5  # Size of each maze cell
WALL_LENGTH = 4.96  # Length of center wall
MAZE_WIDTH = {MAZE_WIDTH}
MAZE_HEIGHT = {MAZE_HEIGHT}

# Generated maze data
maze_data = {maze_grid}

wall_count = 0
wall_types = [wall_center, wall_half, wall_quarter]

def duplicate_wall(original_wall, location, rotation_z=0, name_suffix=""):
    global wall_count
    new_obj = original_wall.copy()
    new_obj.data = original_wall.data.copy()
    new_obj.name = f"{{original_wall.name}}_maze_{{wall_count}}{{name_suffix}}"
    new_obj.location = location
    new_obj.rotation_euler = (0, 0, rotation_z)
    bpy.context.collection.objects.link(new_obj)
    wall_count += 1
    return new_obj

print("Creating maze walls from generated data...")

# Place walls based on maze data
for y in range(MAZE_HEIGHT):
    for x in range(MAZE_WIDTH):
        if maze_data[y][x]:  # If this position should have a wall
            world_x = x * CELL_SIZE
            world_y = y * CELL_SIZE

            # Choose wall type based on position for variety
            wall_type_index = (x + y) % 3
            wall_type = wall_types[wall_type_index]

            # Determine wall orientation based on neighbors
            # Check if this is a horizontal or vertical wall segment
            is_horizontal = False
            is_vertical = False

            # Check horizontal neighbors
            if x > 0 and x < MAZE_WIDTH - 1:
                if maze_data[y][x-1] or maze_data[y][x+1]:
                    is_horizontal = True

            # Check vertical neighbors
            if y > 0 and y < MAZE_HEIGHT - 1:
                if maze_data[y-1][x] or maze_data[y+1][x]:
                    is_vertical = True

            # Place wall with appropriate rotation
            if is_horizontal and not is_vertical:
                # Horizontal wall
                duplicate_wall(wall_type, Vector((world_x, world_y, 0)), 0, f"_h_{{x}}_{{y}}")
            elif is_vertical and not is_horizontal:
                # Vertical wall
                duplicate_wall(wall_type, Vector((world_x, world_y, 0)), 1.5708, f"_v_{{x}}_{{y}}")
            else:
                # Corner or intersection - use default orientation
                duplicate_wall(wall_type, Vector((world_x, world_y, 0)), 0, f"_c_{{x}}_{{y}}")

print(f"Maze created with {{wall_count}} walls")
print("All three wall types used with proper spacing")
'''

print('Creating proper maze walls...')
result = send_blender_command('execute_code', {'code': maze_creation_code})
print('Maze walls created!')

# Create entrance and exit by modifying maze data
entrance_exit_code = '''
import bpy

print("Creating entrance and exit...")

# Create entrance at bottom edge
entrance_x = 1  # First passage cell
entrance_y = 0  # Bottom edge
print(f"Entrance at: ({entrance_x}, {entrance_y})")

# Create exit at top edge
exit_x = 19  # Last passage cell
exit_y = 20   # Top edge
print(f"Exit at: ({exit_x}, {exit_y})")

print("Entrance and exit positions marked!")
'''

print('Creating entrance and exit...')
result = send_blender_command('execute_code', {'code': entrance_exit_code})
print('Entrance and exit created!')

# Find solution path using simple pathfinding
def find_solution_path(maze, start_x, start_y, end_x, end_y):
    """Find path from start to end using BFS"""
    from collections import deque

    queue = deque([(start_x, start_y, [(start_x, start_y)])])
    visited = set()
    visited.add((start_x, start_y))

    directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]

    while queue:
        x, y, path = queue.popleft()

        if x == end_x and y == end_y:
            return path

        for dx, dy in directions:
            nx, ny = x + dx, y + dy

            if (0 <= nx < len(maze[0]) and 0 <= ny < len(maze) and
                (nx, ny) not in visited and not maze[ny][nx]):
                visited.add((nx, ny))
                queue.append((nx, ny, path + [(nx, ny)]))

    return []

# Find solution path
solution_path = find_solution_path(maze_grid, 1, 1, 19, 19)
print(f'Found solution path with {len(solution_path)} steps')

# Create solution visualization and setup scene
solution_setup_code = f'''
import bpy
from mathutils import Vector

CELL_SIZE = 2.5
MAZE_WIDTH = {MAZE_WIDTH}
MAZE_HEIGHT = {MAZE_HEIGHT}

print("Creating solution path...")

# Solution path coordinates
solution_coords = {solution_path}

# Create cylinders for solution path
for i, (x, y) in enumerate(solution_coords):
    world_x = x * CELL_SIZE
    world_y = y * CELL_SIZE

    bpy.ops.mesh.primitive_cylinder_add(
        radius=0.3,
        depth=0.8,
        location=(world_x, world_y, 0.8)
    )

    cylinder = bpy.context.active_object
    cylinder.name = f"PathMarker_{{i}}"

    # Create bright green material
    if not bpy.data.materials.get("PathMaterial"):
        mat = bpy.data.materials.new(name="PathMaterial")
        mat.use_nodes = True
        mat.node_tree.nodes.clear()

        bsdf = mat.node_tree.nodes.new(type="ShaderNodeBsdfPrincipled")
        output = mat.node_tree.nodes.new(type="ShaderNodeOutputMaterial")

        # Bright green color
        bsdf.inputs["Base Color"].default_value = (0.0, 1.0, 0.0, 1.0)
        bsdf.inputs["Emission"].default_value = (0.0, 0.8, 0.0, 1.0)
        bsdf.inputs["Emission Strength"].default_value = 1.0

        mat.node_tree.links.new(bsdf.outputs["BSDF"], output.inputs["Surface"])

    # Apply material
    cylinder.data.materials.append(bpy.data.materials["PathMaterial"])

print(f"Created {{len(solution_coords)}} path markers")

# Setup camera for overhead view
bpy.ops.object.camera_add()
camera = bpy.context.active_object
camera.name = "MazeCamera"

# Position camera overhead
maze_center_x = MAZE_WIDTH * CELL_SIZE / 2
maze_center_y = MAZE_HEIGHT * CELL_SIZE / 2
camera.location = Vector((maze_center_x, maze_center_y, 80))
camera.rotation_euler = (0, 0, 0)
bpy.context.scene.camera = camera

# Add lighting
bpy.ops.object.light_add(type="SUN")
sun = bpy.context.active_object
sun.location = Vector((maze_center_x, maze_center_y, 60))
sun.data.energy = 5

print("Scene setup complete!")
print(f"Maze size: {{MAZE_WIDTH * CELL_SIZE}} x {{MAZE_HEIGHT * CELL_SIZE}} units")
print("Green cylinders show the solution path from entrance to exit")
'''

print('Creating solution path and setting up scene...')
result = send_blender_command('execute_code', {'code': solution_setup_code})
print('Solution path and scene setup complete!')

# Take a screenshot
print('Taking screenshot...')
screenshot_result = send_blender_command('get_viewport_screenshot', {
    'max_size': 1200,
    'filepath': 'proper_maze_with_solution.png',
    'format': 'png'
})
print('Screenshot taken!')
print('Proper maze with recursive backtracking algorithm complete!')
